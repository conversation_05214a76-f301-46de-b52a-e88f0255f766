import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Upload, Copy, RotateCcw, Spark<PERSON>, Lightbulb, Image, Type } from 'lucide-react';
import { ChameleonDemon } from './components/ChameleonDemon';
import TemperatureSlider from './components/TemperatureSlider';

const App = () => {
  // Основные состояния
  const [input, setInput] = useState('');
  const [inputType, setInputType] = useState('image'); // 'image' или 'text'
  const [imagePreview, setImagePreview] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [assistantMood, setAssistantMood] = useState('idle');
  const [isDragging, setIsDragging] = useState(false);
  const [temperature, setTemperature] = useState(0.7);
  const [showConfetti, setShowConfetti] = useState(false);
  const [showMagicParticles, setShowMagicParticles] = useState(false);
  const [successAnimation, setSuccessAnimation] = useState(false);
  const [heroMode, setHeroMode] = useState(false);
  const [celebrationLevel, setCelebrationLevel] = useState(0);
  const [isMobile, setIsMobile] = useState(typeof window !== 'undefined' ? window.innerWidth < 768 : false);
  const [isTablet, setIsTablet] = useState(typeof window !== 'undefined' ? window.innerWidth >= 768 && window.innerWidth < 1024 : false);
  const [isDesktop, setIsDesktop] = useState(typeof window !== 'undefined' ? window.innerWidth >= 1024 : true);
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768
  });
  const [showExample, setShowExample] = useState(true); // Показывать пример

  // Примеры мемных ответов
  const memeExamples = [
    "Вау! Это огонь! 🔥",
    "Кринж... но поддерживаю 😂",
    "Такой ответ - pure gold! 💎",
    "Шикарно! Одобрямс 👍",
    "Это пушка! 💣",
    "Слишком мощно! 🚀",
    "Сочный ответ! 🍉",
    "Хайпово! 🌈",
    "Лютый ответ! 💪",
    "Топчик! 👑"
  ];

  // Реакции персонажа
  useEffect(() => {
    if (isLoading) {
      setAssistantMood('thinking');
    } else if (showResult) {
      setAssistantMood('happy');
      setTimeout(() => setAssistantMood('idle'), 2000);
    } else if (input || imagePreview) {
      setAssistantMood('talking');
    }
  }, [isLoading, showResult, input, imagePreview]);

  // Реакция на изменение тона
  useEffect(() => {
    if (temperature < 0.33) {
      setAssistantMood('cold');
    } else if (temperature < 0.67) {
      setAssistantMood('neutral');
    } else {
      setAssistantMood('hot');
    }

    // Возвращаемся к обычному состоянию через 1 секунду
    const timer = setTimeout(() => {
      if (input || imagePreview) setAssistantMood('talking');
      else setAssistantMood('idle');
    }, 1000);

    return () => clearTimeout(timer);
  }, [temperature]);

  // Скрывать пример при вводе данных
  useEffect(() => {
    if (input.trim() || imagePreview) {
      setShowExample(false);
    } else if (!input.trim() && !imagePreview) {
      const timer = setTimeout(() => {
        setShowExample(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [input, imagePreview]);

  // Screen size detection and responsive updates
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      setScreenSize({ width, height });
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
      setIsDesktop(width >= 1024);
    };

    // Initial call
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Drag and drop handlers
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    const files = e.dataTransfer.files;
    if (files.length > 0 && files[0].type.startsWith('image/')) {
      handleImageUpload(files[0]);
    }
  };

  const handleImageUpload = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target.result);
      setInput('');
      setInputType('image');
    };
    reader.readAsDataURL(file);
  };

  // Получение ответа
  const getReply = async () => {
    if (!input.trim() && !imagePreview) {
      setAssistantMood('confused');
      setTimeout(() => setAssistantMood('idle'), 1000);
      return;
    }

    // Активируем героический режим при начале
    activateHeroMode();
    setIsLoading(true);
    setShowResult(false);
    setAssistantMood('thinking');

    // Создаем магические частицы во время загрузки
    createMagicParticles();

    // Симуляция API-запроса с более драматичной паузой
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Генерация ответа
    const responses = [
      "🔥 ОГОНЬ! Это просто шедевр! 🎨✨",
      "💎 ВАУ! Гениальная идея! Я в восторге! �",
      "⚡ МОЩНО! Такой подход - это прорыв! 💪",
      "🌟 НЕВЕРОЯТНО! Ты превзошел все ожидания! 🎯",
      "🎉 БРАВО! Это именно то, что нужно! 👑",
      "🔮 МАГИЯ! Как ты это придумал? 🧙‍♂️"
    ];

    const selectedResponse = responses[Math.floor(Math.random() * responses.length)];
    setResponse(selectedResponse);
    setShowResult(true);
    setIsLoading(false);

    // Запускаем празднование успеха
    triggerSuccessCelebration();
  };

  // Копирование в буфер
  const copyToClipboard = async () => {
    await navigator.clipboard.writeText(response);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  };

  // Сброс формы
  const resetForm = () => {
    setShowResult(false);
    setInput('');
    setImagePreview(null);
    setIsCopied(false);
    setShowExample(true);
    setSuccessAnimation(false);
    setShowConfetti(false);
    setShowMagicParticles(false);
    setHeroMode(false);
    setCelebrationLevel(0);
  };

  // Создание конфетти
  const createConfetti = () => {
    setShowConfetti(true);
    setTimeout(() => setShowConfetti(false), 3000);
  };

  // Создание магических частиц
  const createMagicParticles = () => {
    setShowMagicParticles(true);
    setTimeout(() => setShowMagicParticles(false), 2000);
  };

  // Запуск празднования успеха
  const triggerSuccessCelebration = () => {
    setSuccessAnimation(true);
    setAssistantMood('excited');
    createConfetti();
    createMagicParticles();

    // Увеличиваем уровень празднования
    setCelebrationLevel(prev => Math.min(prev + 1, 3));

    setTimeout(() => {
      setSuccessAnimation(false);
      setAssistantMood('happy');
    }, 2000);
  };

  // Активация героического режима
  const activateHeroMode = () => {
    setHeroMode(true);
    setAssistantMood('heroic');
    setTimeout(() => {
      setHeroMode(false);
      setAssistantMood('idle');
    }, 3000);
  };

  // Адаптивные размеры персонажа - компактные для десктопа
  const getCharacterSize = () => {
    if (screenSize.width < 375) return 100;
    if (screenSize.width < 640) return 120;
    if (screenSize.width < 768) return 140;
    if (screenSize.width < 1024) return 160;
    // Компактные размеры для десктопа чтобы все поместилось
    if (screenSize.width < 1280) return 140;
    if (screenSize.width < 1536) return 160;
    if (screenSize.width < 1920) return 180;
    if (screenSize.width < 2560) return 200;
    return 220;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 text-gray-900 dark:text-white relative overflow-hidden">
      {/* Анимированный фон */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-tr from-blue-100/30 dark:from-blue-900/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-tl from-purple-100/30 dark:from-purple-900/10 to-transparent rounded-full blur-3xl"></div>
      </div>

      {/* Основной контент */}
      <section className="desktop-hero container-responsive spacing-responsive relative z-10">
        {/* Персонаж с реакциями */}
        <motion.div
          className="mb-1 w-20 h-20 xs:w-24 xs:h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:character-compact xl:character-compact 2xl:character-compact flex items-center justify-center"
          animate={{
            scale: assistantMood === 'thinking' ? [1, 1.05, 1] : 1,
            rotate: assistantMood === 'confused' ? [0, 5, -5, 0] : 0
          }}
          transition={{
            duration: 0.5,
            repeat: assistantMood === 'thinking' ? Infinity : 0
          }}
        >
          <ChameleonDemon
            mood={assistantMood}
            temperature={temperature}
            size={getCharacterSize()}
          />

          {/* Индикаторы состояния */}
          {assistantMood === 'thinking' && (
            <motion.div
              className="absolute -top-2 -right-2 bg-white dark:bg-gray-800 rounded-full p-2 shadow-lg"
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
            >
              <Sparkles className="w-5 h-5 text-yellow-400" />
            </motion.div>
          )}
        </motion.div>

        {/* Заголовок */}
        <motion.div
          className="text-center mb-1 px-2"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <motion.h1
            className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-black leading-tight mb-2"
            animate={{
              backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            style={{
              background: "linear-gradient(90deg, #3B82F6, #8B5CF6, #EC4899, #F59E0B, #3B82F6)",
              backgroundSize: "200% 100%",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text"
            }}
          >
            Тупишь в переписке?
          </motion.h1>

          <motion.div
            className="flex items-center justify-center gap-2 sm:gap-3 flex-wrap"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.span
              className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-800 dark:text-gray-200"
              whileHover={{ scale: 1.05 }}
            >
              Помогу за
            </motion.span>

            <motion.div
              className="relative inline-block"
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <span className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black bg-gradient-to-r from-yellow-400 via-red-500 to-pink-500 bg-clip-text text-transparent">
                1 секунду!
              </span>

              {/* Декоративные элементы */}
              <motion.div
                className="absolute -top-1 -right-1 w-3 h-3 sm:w-4 sm:h-4 bg-yellow-400 rounded-full"
                animate={{
                  scale: [0, 1, 0],
                  opacity: [0, 1, 0]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: 0.5
                }}
              />

              <motion.div
                className="absolute -bottom-1 -left-1 w-2 h-2 sm:w-3 sm:h-3 bg-pink-500 rounded-full"
                animate={{
                  scale: [0, 1, 0],
                  opacity: [0, 1, 0]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: 1
                }}
              />
            </motion.div>

            <motion.span
              className="text-2xl sm:text-3xl md:text-4xl"
              animate={{
                rotate: [0, 10, -10, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 0.5
              }}
            >
              ⚡
            </motion.span>
          </motion.div>
        </motion.div>

        {/* Описание */}
        <motion.p
          className="text-sm sm:text-base lg:text-lg text-gray-600 dark:text-gray-400 text-center mb-1 max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg px-2 font-medium"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <span className="inline-block mr-2">📱</span>
          Просто скинь скрин или напиши текст
          <span className="inline-block ml-2">✨</span>
        </motion.p>

        {/* Пример ответа ИИ */}
        <AnimatePresence>
          {showExample && !input.trim() && !imagePreview && (
            <motion.div
              className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg mb-1 p-2 sm:p-3 lg:p-3 rounded-lg sm:rounded-xl bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 border border-indigo-200/50 dark:border-purple-700/30 backdrop-blur-sm"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-start gap-2 sm:gap-3">
                <div className="bg-blue-500 text-white rounded-full p-1.5 sm:p-2 flex-shrink-0">
                  <Sparkles className="w-3 h-3 sm:w-4 sm:h-4" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-blue-600 dark:text-blue-400 mb-1">Пример ответа:</p>
                  <p className="text-xs sm:text-sm lg:text-base text-gray-700 dark:text-gray-200 font-bold break-words">
                    {memeExamples[Math.floor(Math.random() * memeExamples.length)]}
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Переключатель ввода */}
        <div className="flex mb-1 bg-white/80 dark:bg-gray-800/80 rounded-full p-1 backdrop-blur-sm shadow-lg">
          <button
            className={`px-3 py-2 sm:px-4 sm:py-2 lg:px-6 lg:py-3 text-sm lg:text-base rounded-full flex items-center gap-2 transition-all duration-200 ${
              inputType === 'image'
                ? 'bg-blue-500 text-white shadow-md'
                : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
            onClick={() => setInputType('image')}
          >
            <Image className="w-3 h-3 sm:w-4 sm:h-4" />
            <span className="hidden xs:inline">Фото</span>
          </button>
          <button
            className={`px-3 py-2 sm:px-4 sm:py-2 lg:px-6 lg:py-3 text-sm lg:text-base rounded-full flex items-center gap-2 transition-all duration-200 ${
              inputType === 'text'
                ? 'bg-blue-500 text-white shadow-md'
                : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
            onClick={() => setInputType('text')}
          >
            <Type className="w-3 h-3 sm:w-4 sm:h-4" />
            <span className="hidden xs:inline">Текст</span>
          </button>
        </div>

        {/* Поле ввода */}
        <motion.div
          className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl mb-1 input-area-responsive input-compact"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {inputType === 'image' ? (
            <div
              className={`h-24 xs:h-28 sm:h-32 md:h-36 lg:h-40 xl:h-44 rounded-lg sm:rounded-xl border-2 border-dashed flex items-center justify-center cursor-pointer transition-all duration-300 shadow-lg hover:shadow-xl ${
                isDragging
                  ? 'border-blue-500 bg-blue-50/50 dark:bg-blue-900/10 scale-105 glow'
                  : 'border-gray-300 hover:border-blue-400 bg-white/80 dark:bg-gray-800/80'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => document.getElementById('fileInput')?.click()}
            >
              <input
                id="fileInput"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={(e) => e.target.files[0] && handleImageUpload(e.target.files[0])}
              />

              {imagePreview ? (
                <div className="relative h-full w-full">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="w-full h-full object-contain p-2 rounded-lg"
                  />
                  <button
                    className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 sm:p-2 transition-colors shadow-lg"
                    onClick={(e) => {
                      e.stopPropagation();
                      setImagePreview(null);
                    }}
                  >
                    <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              ) : (
                <div className="text-center p-fluid-2">
                  <Upload className="w-6 h-6 sm:w-8 sm:h-8 mx-auto text-gray-400 mb-2" />
                  <p className="text-responsive-sm font-medium text-gray-700 dark:text-gray-300">Перетащи скриншот сюда</p>
                  <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-1">или нажми для выбора</p>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg sm:rounded-xl border border-gray-200 dark:border-gray-700 hover:border-blue-400/50 focus-within:border-blue-500 focus-within:glow transition-all duration-300 backdrop-blur-sm shadow-lg hover:shadow-xl">
              <textarea
                className="w-full h-20 xs:h-24 sm:h-28 md:h-32 lg:h-36 xl:h-40 p-3 sm:p-4 lg:p-5 bg-transparent text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none resize-none text-sm sm:text-base lg:text-lg leading-relaxed"
                placeholder="Введи текст сообщения..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onFocus={() => setAssistantMood('talking')}
              />
            </div>
          )}
        </motion.div>

        {/* Основная CTA кнопка для Desktop */}
        {!isMobile && (
          <div className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl mb-1">
            <motion.button
              onClick={getReply}
              disabled={isLoading || (!input.trim() && !imagePreview)}
              className={`w-full px-4 py-2 sm:px-6 sm:py-3 lg:px-8 lg:py-3 text-sm lg:text-base rounded-lg sm:rounded-xl font-semibold transition-all duration-300 shadow-lg cta-desktop ${
                (!input.trim() && !imagePreview)
                  ? 'bg-gray-400 cursor-not-allowed opacity-60'
                  : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:opacity-90 hover:shadow-xl glow hover:from-blue-600 hover:to-purple-700'
              }`}
              whileHover={(!input.trim() && !imagePreview) ? {} : { scale: 1.02, y: -2 }}
              whileTap={(!input.trim() && !imagePreview) ? {} : { scale: 0.98 }}
            >
              {isLoading ? (
                <span className="flex items-center justify-center gap-2 lg:gap-3">
                  <motion.div
                    className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 border-2 border-white border-t-transparent rounded-full"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                  <span className="text-sm lg:text-base">Думаю...</span>
                </span>
              ) : (
                <span className="flex items-center justify-center gap-2 lg:gap-3">
                  <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
                  <span className="text-sm lg:text-base font-semibold">Получить ответ!</span>
                </span>
              )}
            </motion.button>
          </div>
        )}

        {/* Мини-слайдер температуры */}
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl mb-1 flex items-center justify-center">
          <TemperatureSlider
            temperature={temperature}
            onChange={setTemperature}
            onMoodChange={setAssistantMood}
          />
        </div>

        {/* Блок результата */}
        <AnimatePresence>
          {showResult && (
            <motion.div
              className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-2xl xl:max-w-3xl 2xl:max-w-4xl mt-fluid-1 card-responsive bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg border border-white/30 dark:border-gray-700/30 shadow-xl"
              initial={{ opacity: 0, y: 20, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <h3 className="text-responsive-lg font-semibold mb-fluid-2 flex items-center gap-3 lg:gap-4 text-blue-500">
                <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 xl:w-7 xl:h-7" /> Ваш ИИдеальный ответ:
              </h3>

              <motion.p
                className="text-responsive-base text-gray-700 dark:text-gray-300 mb-fluid-3 whitespace-pre-wrap leading-relaxed"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                {response}
              </motion.p>

              <div className="flex flex-col xs:flex-row gap-3 lg:gap-4">
                <button
                  onClick={copyToClipboard}
                  className="flex-1 btn-responsive rounded-lg lg:rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center gap-3 lg:gap-4 shadow-md"
                >
                  <Copy className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 xl:w-6 xl:h-6" />
                  <span className="text-responsive-base">{isCopied ? 'Скопировано!' : 'Копировать'}</span>
                </button>

                <button
                  onClick={getReply}
                  className="flex-1 btn-responsive rounded-lg lg:rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:opacity-90 transition-opacity flex items-center justify-center gap-3 lg:gap-4 shadow-md"
                >
                  <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 xl:w-6 xl:h-6" />
                  <span className="text-responsive-base">Другой вариант</span>
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Sticky CTA для мобильных */}
        {(isMobile && (input.trim() || imagePreview)) && (
          <div className="fixed bottom-0 left-0 right-0 p-fluid-2 bg-gradient-to-t from-white/95 to-transparent dark:from-gray-900/95 backdrop-blur-md z-20 border-t border-gray-200/50 dark:border-gray-700/50">
            <div className="container-responsive">
              <button
                onClick={getReply}
                disabled={isLoading}
                className={`w-full btn-responsive bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium rounded-xl shadow-lg glow transition-all duration-300 ${
                  isLoading ? 'opacity-80' : 'hover:shadow-xl'
                }`}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center gap-2">
                    <motion.div
                      className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity }}
                    />
                    <span className="text-responsive-sm">Думаю...</span>
                  </span>
                ) : (
                  <span className="flex items-center justify-center gap-2">
                    <Sparkles className="w-4 h-4" />
                    <span className="text-responsive-sm font-semibold">Получить ответ!</span>
                  </span>
                )}
              </button>
            </div>
          </div>
        )}

        {/* Кнопка сброса */}
        {(input.trim() || imagePreview) && !isMobile && (
          <motion.button
            onClick={resetForm}
            className="fixed bottom-4 sm:bottom-6 right-4 sm:right-6 p-2 sm:p-3 bg-gray-800/90 dark:bg-gray-200/90 text-white dark:text-gray-900 rounded-full shadow-lg backdrop-blur-md hover:shadow-xl transition-all duration-300"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            whileHover={{ scale: 1.1, rotate: -15 }}
            whileTap={{ scale: 0.9 }}
          >
            <RotateCcw className="w-4 h-4 sm:w-5 sm:h-5" />
          </motion.button>
        )}
      </section>
    </div>
  );
};

export default App;
